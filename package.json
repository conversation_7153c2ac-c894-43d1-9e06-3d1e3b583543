{"name": "king-collectibles-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:ws": "tsx server/websocket-server.ts", "dev:full": "concurrently \"npm run dev\" \"npm run dev:ws\"", "build": "next build", "build:with-db": "npm run db:deploy && next build", "build:production": "npm run db:deploy:production && next build", "build:docker": "next build", "build:watch": "next build --watch", "start": "next start", "start:ws": "tsx server/websocket-server.ts", "start:full": "concurrently \"npm run start\" \"npm run start:ws\"", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:migrate:reset": "prisma migrate reset --force", "db:migrate:status": "prisma migrate status", "db:seed": "tsx prisma/seed.ts", "db:seed:check": "tsx scripts/check-seed-status.ts", "db:seed:force": "tsx prisma/seed.ts --force", "db:reset": "npm run db:migrate:reset && npm run db:seed", "db:deploy": "npm run db:generate && npm run db:migrate:deploy && npm run db:seed:smart", "db:deploy:production": "npm run db:generate && npm run db:migrate:deploy && npm run db:seed:production", "db:seed:smart": "tsx scripts/smart-seed.ts", "db:seed:production": "tsx scripts/production-seed.ts", "db:status": "npm run db:migrate:status && npm run db:seed:check", "predeploy": "tsx scripts/deploy-check.ts", "deploy:staging": "npm run predeploy && npm run db:deploy && npm run build && npm run start", "deploy:production": "npm run predeploy && npm run db:deploy:production && npm run build:production", "postdeploy": "npm run db:status", "deploy:check": "tsx scripts/deploy-check.ts", "clean-currency-duplicates": "tsx server/scripts/cleanDuplicateCurrencyRates.ts", "maintenance:clean": "tsx scripts/maintenance-clean.ts", "rollback": "tsx scripts/rollback.ts --data", "rollback:migrations": "tsx scripts/rollback.ts --migrations --force", "rollback:dry-run": "tsx scripts/rollback.ts --data --dry-run", "emergency:restore": "tsx scripts/rollback.ts --data --force && npm run db:seed:production"}, "dependencies": {"@chakra-ui/cli": "^3.16.1", "@chakra-ui/react": "^3.16.1", "@emotion/react": "^11.14.0", "@fontsource/poppins": "^5.2.6", "@hono/swagger-ui": "0.5.0", "@hono/zod-openapi": "^0.19.6", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.8.2", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "@types/lodash": "^4.17.17", "@types/multer": "^1.4.13", "@types/negotiator": "^0.6.3", "@types/nodemailer": "^6.4.17", "add": "^2.0.6", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.6.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "google-auth-library": "^9.15.1", "hono": "^4.7.9", "lodash": "^4.17.21", "lucide-react": "^0.514.0", "multer": "^2.0.1", "negotiator": "^1.0.0", "next": "15.3.1", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "node-cron": "^4.2.1", "nodemailer": "^6.10.1", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.57.0", "react-icons": "^5.5.0", "react-select": "^5.10.1", "snippet": "^0.1.0", "swiper": "^11.2.6", "use-mask-input": "^3.4.2", "xendit-node": "^7.0.0", "zod": "^3.25.61", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bcryptjs": "^3.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/ws": "^8.18.1", "concurrently": "^9.2.0", "eslint": "^9", "eslint-config-next": "15.3.1", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "tsx": "^4.20.3", "typescript": "^5"}}