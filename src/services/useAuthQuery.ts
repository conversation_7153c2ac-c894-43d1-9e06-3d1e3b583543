import { useMutation, UseMutationResult, useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import axios, { AxiosResponse } from "axios";
import { toaster } from "@/components/ui/toaster";
import { mockMutation } from "@/utils/axios-mock";
import { defaultLocale } from "@/i18n";

// Types
interface AuthResponse {
  status: boolean;
  message: string;
  data?: any;
}

interface RegisterData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword: string;
}

interface LoginData {
  emailPhoneNumber: string;
  password: string;
}

// API Base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api/v1';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

apiClient.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => Promise.reject(error)
);

apiClient.interceptors.response.use(
  (response: AxiosResponse) => response.data,
  (error) => {
    const message = error.response?.data?.message || error.message || 'An error occurred';
    return Promise.reject(new Error(message));
  }
);

export const useAuthenticatedApi = () => {
  const { data: session, update } = useSession();

  // If session is not available, return unauthenticated client for public endpoints
  if (!session?.accessToken) {
    return apiClient;
  }

  const authenticatedClient = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
  });

  // Request interceptor to add auth header
  authenticatedClient.interceptors.request.use(
    (config) => {
      if (session?.accessToken) {
        config.headers.Authorization = `Bearer ${session.accessToken}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for error handling and token refresh
  authenticatedClient.interceptors.response.use(
    (response: AxiosResponse) => response.data,
    async (error) => {
      const originalRequest = error.config;

      // Handle 401 errors (token expired)
      if (error.response?.status === 401 && session?.refreshToken && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          // Trigger token refresh via NextAuth
          const updatedSession = await update({ trigger: 'update' });

          if (updatedSession?.accessToken) {
            // Update the authorization header with new token
            originalRequest.headers.Authorization = `Bearer ${updatedSession.accessToken}`;
            // Retry the original request with new token
            return authenticatedClient.request(originalRequest);
          } else {
            throw new Error('Failed to refresh token - no access token received');
          }
        } catch (refreshError) {
          // Clear session and redirect to login
          if (typeof window !== 'undefined') {
            toaster.create({
              title: "Session Expired",
              description: "Please log in again to continue.",
              type: "warning",
            });

            // Redirect to login page
            window.location.href = `/${defaultLocale}/auth/login`;
          }

          return Promise.reject(new Error('Authentication failed - please log in again'));
        }
      }

      const message = error.response?.data?.message || error.message || 'An error occurred';
      return Promise.reject(new Error(message));
    }
  );

  return authenticatedClient;
};

// Hook for strictly authenticated API calls (returns null if not authenticated)
export const useStrictAuthenticatedApi = () => {
  const { data: session, update } = useSession();

  // If session is not available, return null to prevent API calls
  if (!session?.accessToken) {
    return null;
  }

  const authenticatedClient = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
  });

  // Request interceptor to add auth header
  authenticatedClient.interceptors.request.use(
    (config) => {
      if (session?.accessToken) {
        config.headers.Authorization = `Bearer ${session.accessToken}`;
      }
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for error handling and token refresh
  authenticatedClient.interceptors.response.use(
    (response: AxiosResponse) => response.data,
    async (error) => {
      const originalRequest = error.config;

      // Handle 401 errors (token expired)
      if (error.response?.status === 401 && session?.refreshToken && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          // Trigger token refresh via NextAuth
          const updatedSession = await update({ trigger: 'update' });

          if (updatedSession?.accessToken) {
            // Update the authorization header with new token
            originalRequest.headers.Authorization = `Bearer ${updatedSession.accessToken}`;
            // Retry the original request with new token
            return authenticatedClient.request(originalRequest);
          } else {
            throw new Error('Failed to refresh token - no access token received');
          }
        } catch (refreshError) {
          // Clear session and redirect to login
          if (typeof window !== 'undefined') {
            toaster.create({
              title: "Session Expired",
              description: "Please log in again to continue.",
              type: "warning",
            });

            // Redirect to login page
            window.location.href = `/${defaultLocale}/auth/login`;
          }

          return Promise.reject(new Error('Authentication failed - please log in again'));
        }
      }

      const message = error.response?.data?.message || error.message || 'An error occurred';
      return Promise.reject(new Error(message));
    }
  );

  return authenticatedClient;
};

interface MutationRegister {
  onError: (error: unknown) => void;
  onSuccess: (data: any) => void;
}

export const MutationRegister = ({
  onError,
  onSuccess,
}: MutationRegister): UseMutationResult<any, Error, any> => {
  const url = `/auth/register`;

  const mutationFn = async (data: any) => {
    return await mockMutation(url, data, "post" );
  };

  const resMutation = useMutation({
    mutationKey: [url],
    mutationFn: mutationFn,
    onError,
    onSuccess,
  }) as UseMutationResult;

  return resMutation;
};




