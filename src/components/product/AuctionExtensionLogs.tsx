import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  Spinner,
  Icon,
  Button,
  useDisclosure,
  Card,
  CardBody,
  CardHeader,
  Heading,
  SimpleGrid,
  Separator
} from '@chakra-ui/react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ser, FaRobot, FaChevronDown, FaChevronUp, FaHistory } from 'react-icons/fa';
import { useProductExtensionLogsQuery, useProductExtensionStatsQuery } from '../../services/useAuctionExtensionQuery';
import { formatDistanceToNow, format } from 'date-fns';
import { Alert } from '../ui/alert';

interface AuctionExtensionLogsProps {
  productId: string;
}

const AuctionExtensionLogs: React.FC<AuctionExtensionLogsProps> = ({ productId }) => {
  const { open, onOpen, onClose } = useDisclosure();

  const {
    data: extensionLogs,
    isLoading: logsLoading,
    error: logsError
  } = useProductExtensionLogsQuery(productId);

  const {
    data: extensionStats,
    isLoading: statsLoading,
    error: statsError
  } = useProductExtensionStatsQuery(productId);

  if (logsLoading || statsLoading) {
    return (
      <Card.Root>
        <CardBody>
          <HStack justify="center" py={4}>
            <Spinner size="sm" />
            <Text>Loading extension logs...</Text>
          </HStack>
        </CardBody>
      </Card.Root>
    );
  }

  if (logsError || statsError) {
    return (
      <Alert status="error">
        <Alert.Icon />
        Failed to load auction extension data
      </Alert>
    );
  }

  if (!extensionLogs || extensionLogs.totalExtensions === 0) {
    return (
      <Card.Root>
        <CardBody>
          <Text color="gray.500" textAlign="center" py={4}>
            No auction extensions have occurred for this product.
          </Text>
        </CardBody>
      </Card.Root>
    );
  }

  return (
    <Card.Root>
      <CardHeader>
        <HStack justify="space-between" align="center">
          <HStack>
            <Icon as={FaHistory} color="blue.500" />
            <Heading size="md">Auction Extensions</Heading>
            <Badge colorScheme="blue" variant="subtle">
              {extensionLogs.totalExtensions} total
            </Badge>
          </HStack>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => {
              open ? onClose() : onOpen();
            }}
          >
            <Icon as={open ? FaChevronUp : FaChevronDown} />
            {open ? 'Hide' : 'Show'} Details
          </Button>
        </HStack>
      </CardHeader>

      <CardBody>
        {/* Extension Statistics */}
        {extensionStats && (
          <SimpleGrid columns={{ base: 2, md: 4 }} gap={4} mb={6}>
            <Box textAlign="center" p={4} bg="gray.50" borderRadius="md">
              <Text fontSize="sm" color="gray.600" mb={1}>Total Extensions</Text>
              <Text fontSize="lg" fontWeight="bold">{extensionStats.totalExtensions}</Text>
            </Box>
            <Box textAlign="center" p={4} bg="gray.50" borderRadius="md">
              <Text fontSize="sm" color="gray.600" mb={1}>Total Time Added</Text>
              <Text fontSize="lg" fontWeight="bold">{extensionStats.totalExtendedMinutes}m</Text>
            </Box>
            <Box textAlign="center" p={4} bg="gray.50" borderRadius="md">
              <Text fontSize="sm" color="gray.600" mb={1}>Avg Trigger Bid</Text>
              <Text fontSize="lg" fontWeight="bold">${extensionStats.averageTriggerBid.toFixed(2)}</Text>
            </Box>
            <Box textAlign="center" p={4} bg="gray.50" borderRadius="md">
              <Text fontSize="sm" color="gray.600" mb={1}>Highest Trigger</Text>
              <Text fontSize="lg" fontWeight="bold">${extensionStats.highestTriggerBid.toFixed(2)}</Text>
            </Box>
          </SimpleGrid>
        )}

        {open && (
          <VStack gap={4} align="stretch">
            <Separator />

            {/* Extension Type Breakdown */}
            {extensionStats && extensionStats.extensionsByType.length > 0 && (
              <Box>
                <Text fontWeight="semibold" mb={2} fontSize="sm" color="gray.600">
                  Extensions by Type:
                </Text>
                <HStack gap={4}>
                  {extensionStats.extensionsByType.map((type) => (
                    <HStack key={type.triggeredBy}>
                      <Icon
                        as={type.triggeredBy === 'auto-bid' ? FaRobot : FaUser}
                        color={type.triggeredBy === 'auto-bid' ? 'purple.500' : 'blue.500'}
                        boxSize={3}
                      />
                      <Text fontSize="sm">
                        {type.triggeredBy === 'auto-bid' ? 'Auto-bid' : 'Manual'}: {type.count}
                      </Text>
                    </HStack>
                  ))}
                </HStack>
              </Box>
            )}

            <Separator />

            <VStack gap={3} align="stretch">
              <Text fontWeight="semibold" fontSize="sm" color="gray.600">
                Extension History:
              </Text>

              {extensionLogs.extensionLogs.map((log) => (
                <Box
                  key={log.id}
                  p={4}
                  bg="gray.50"
                  borderRadius="md"
                  border="1px solid"
                  borderColor="gray.200"
                >
                  <HStack justify="space-between" align="start" mb={2}>
                    <HStack>
                      <Icon
                        as={log.triggeredBy === 'auto-bid' ? FaRobot : FaUser}
                        color={log.triggeredBy === 'auto-bid' ? 'purple.500' : 'blue.500'}
                      />
                      <Badge
                        colorScheme={log.triggeredBy === 'auto-bid' ? 'purple' : 'blue'}
                        variant="subtle"
                      >
                        {log.triggeredBy === 'auto-bid' ? 'Auto-bid' : 'Manual'}
                      </Badge>
                    </HStack>
                    <Text fontSize="xs" color="gray.500">
                      {formatDistanceToNow(new Date(log.createdAt), { addSuffix: true })}
                    </Text>
                  </HStack>

                  <VStack align="start" gap={1}>
                    <HStack>
                      <Icon as={FaClock} boxSize={3} color="gray.500" />
                      <Text fontSize="sm">
                        Extended by <strong>{log.extendedMinutes} minutes</strong>
                      </Text>
                    </HStack>

                    <Text fontSize="xs" color="gray.600">
                      From: {format(new Date(log.previousEndDate), 'MMM dd, yyyy HH:mm')}
                    </Text>
                    <Text fontSize="xs" color="gray.600">
                      To: {format(new Date(log.newEndDate), 'MMM dd, yyyy HH:mm')}
                    </Text>

                    <Text fontSize="xs" color="gray.600">
                      Triggered by bid: <strong>${log.triggerBidAmount.toFixed(2)}</strong>
                    </Text>

                    {log.triggeredBidder && (
                      <Text fontSize="xs" color="gray.600">
                        Bidder: {log.triggeredBidder.firstName} {log.triggeredBidder.lastName}
                      </Text>
                    )}
                  </VStack>
                </Box>
              ))}
            </VStack>
          </VStack>
        )}
      </CardBody>
    </Card.Root>
  );
};

export default AuctionExtensionLogs;
