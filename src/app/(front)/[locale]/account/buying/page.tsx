'use client'
import React, { useState } from 'react'
import {
  Box,
  Button,
  Heading,
  Text,
  VStack,
  HStack,
  Image,
  Badge,
  Grid,
  Skeleton,
  Flex,
  Icon,
  Card,
  Input,
  InputGroup,
} from '@chakra-ui/react'
import { Alert } from '@/components/ui/alert'

import {
  FaEye,
  FaShoppingBag,
  FaCreditCard,
  FaCalendarAlt,
  FaUser,
  FaChevronLeft,
  FaChevronRight,
  FaTimesCircle,
  FaSearch,
} from 'react-icons/fa'
import { useOrdersQuery, Order, OrdersQueryParams } from '@/services/useOrderQuery'
import { useRouter } from 'next/navigation'
import FormSelectField, { SelectOption } from '@/components/ui/form/FormSelectField'
import { SingleValue } from 'react-select'
import { useTranslations } from 'next-intl'
import { useSession } from 'next-auth/react'
import { useCurrencyLanguage } from '@/contexts/CurrencyLanguageContext'
import { formatDistanceToNow } from 'date-fns'
import { id, enUS } from 'date-fns/locale'

// Status options - will be translated in component
const getStatusOptions = (t: any): SelectOption[] => [
  { value: 'all', label: t('Account.allStatus') || 'All Status' },
  { value: 'pending', label: t('Account.pending') || 'Pending' },
  { value: 'processing', label: t('Account.processing') || 'Processing' },
  { value: 'shipped', label: t('Account.shipped') || 'Shipped' },
  { value: 'delivered', label: t('Account.delivered') || 'Delivered' },
  { value: 'cancelled', label: t('Account.cancelled') || 'Cancelled' },
]

// Payment status options - will be translated in component
const getPaymentStatusOptions = (t: any): SelectOption[] => [
  { value: 'all', label: t('Account.allPaymentStatus') || 'All Payment Status' },
  { value: 'pending', label: t('Account.pendingPayment') || 'Pending Payment' },
  { value: 'paid', label: t('Account.paid') || 'Paid' },
  { value: 'failed', label: t('Account.failed') || 'Failed' },
  { value: 'refunded', label: t('Account.refunded') || 'Refunded' },
]

// Helper function to get status color
const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'orange'
    case 'processing': return 'blue'
    case 'shipped': return 'purple'
    case 'delivered': return 'green'
    case 'cancelled': return 'red'
    default: return 'gray'
  }
}

// Helper function to get payment status color
const getPaymentStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'orange'
    case 'paid': return 'green'
    case 'failed': return 'red'
    case 'refunded': return 'purple'
    default: return 'gray'
  }
}

const AccountBuyingPage = () => {
  const router = useRouter()
  const t = useTranslations()
  const { data: session, status: sessionStatus } = useSession()
  const { formatPrice, language } = useCurrencyLanguage()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [paymentStatusFilter, setPaymentStatusFilter] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState(1)

  // Query parameters for user's orders
  const queryParams: OrdersQueryParams = {
    page: currentPage,
    limit: 10,
    status: statusFilter !== 'all' ? statusFilter as any : undefined,
    paymentStatus: paymentStatusFilter !== 'all' ? paymentStatusFilter as any : undefined,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  }

  const { data: ordersData, isLoading, error } = useOrdersQuery(queryParams)

  // Get translated options
  const statusOptions = getStatusOptions(t)
  const paymentStatusOptions = getPaymentStatusOptions(t)

  const handleStatusChange = (newValue: any) => {
    const option = newValue as SingleValue<SelectOption>
    setStatusFilter(option?.value || 'all')
    setCurrentPage(1)
  }

  const handlePaymentStatusChange = (newValue: any) => {
    const option = newValue as SingleValue<SelectOption>
    setPaymentStatusFilter(option?.value || 'all')
    setCurrentPage(1)
  }

  const handleViewOrder = (orderId: string) => {
    router.push(`/order-tracking/${orderId}`)
  }

  const handlePayNow = (order: Order) => {
    // Check if order has payment info with invoice URL
    if ((order as any).payment?.invoiceUrl) {
      window.open((order as any).payment.invoiceUrl, '_blank')
    } else {
      router.push(`/order-tracking/${order.id}`)
    }
  }

  const getFormattedDate = (date: string) => {
    const locale = language === 'id' ? id : enUS
    return formatDistanceToNow(new Date(date), {
      addSuffix: true,
      locale
    })
  }

  if (sessionStatus === "loading") {
    return (
      <VStack gap={6} align="stretch">
        <Card.Root bg="white" shadow="sm" borderRadius="xl">
          <Card.Body p={6}>
            <Skeleton height="400px" />
            <Skeleton height="400px" mt={4} />
            <Skeleton height="400px" mt={2} />
          </Card.Body>
        </Card.Root>
      </VStack>
    )
  }

  if (!session) {
    return (
      <Alert status="warning">
        <Alert.Icon status="warning" />
        <VStack align="start" flex={1}>
          <Alert.Title>{t('Auth.pleaseLogin')}</Alert.Title>
          <Alert.Description>
            {t('Account.loginToViewOrders')}
          </Alert.Description>
        </VStack>
      </Alert>
    )
  }

  return (
    <VStack gap={6} align="stretch">
      {/* Filters and Search */}
      <Card.Root bg="white" shadow="sm" borderRadius="xl">
        <Card.Body p={6}>
          <VStack gap={4} align="stretch">
            {/* Search */}
            <InputGroup
              startElement={
                <Icon color="gray.400">
                  <FaSearch />
                </Icon>
              }
            >
              <Input
                placeholder="Search your orders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                bg="gray.50"
                border="none"
                _focus={{ bg: 'white', shadow: 'sm' }}
              />
            </InputGroup>

            {/* Filters */}
            <Grid templateColumns={{ base: '1fr', md: '1fr 1fr' }} gap={4}>
              <FormSelectField
                label="Order Status"
                options={statusOptions}
                value={statusOptions.find(opt => opt.value === statusFilter)}
                onChange={handleStatusChange}
                placeholder="Select status..."
              />
              <FormSelectField
                label="Payment Status"
                options={paymentStatusOptions}
                value={paymentStatusOptions.find(opt => opt.value === paymentStatusFilter)}
                onChange={handlePaymentStatusChange}
                placeholder="Select payment status..."
              />
            </Grid>
          </VStack>
        </Card.Body>
      </Card.Root>

      {/* Orders List */}
      {isLoading ? (
        <VStack gap={4} align="stretch">
          {Array.from({ length: 5 }).map((_, index) => (
            <Card.Root key={index} bg="white" shadow="sm" borderRadius="xl">
              <Card.Body p={6}>
                <Skeleton height="150px" />
              </Card.Body>
            </Card.Root>
          ))}
        </VStack>
      ) : error ? (
        <Card.Root bg="white" shadow="sm" borderRadius="xl">
          <Card.Body p={8}>
            <VStack gap={4} textAlign="center">
              <Icon color="red.500" fontSize="4xl">
                <FaTimesCircle />
              </Icon>
              <Heading size="md" color="red.600">
                Failed to load orders
              </Heading>
              <Text color="gray.600">
                Please try again later
              </Text>
            </VStack>
          </Card.Body>
        </Card.Root>
      ) : !ordersData?.orders.length ? (
        <Card.Root bg="white" shadow="sm" borderRadius="xl">
          <Card.Body p={8}>
            <VStack gap={6} textAlign="center">
              <Icon color="gray.400" fontSize="5xl">
                <FaShoppingBag />
              </Icon>
              <VStack gap={2}>
                <Heading size="lg" color="gray.600">
                  No orders found
                </Heading>
                <Text color="gray.500">
                  Start shopping to see your orders here
                </Text>
              </VStack>
              <Button
                colorScheme="blue"
                size="lg"
                onClick={() => router.push('/marketplace')}
              >
                <HStack gap={2}>
                  <Icon><FaShoppingBag /></Icon>
                  <Text>Start Shopping</Text>
                </HStack>
              </Button>
            </VStack>
          </Card.Body>
        </Card.Root>
      ) : (
        <VStack gap={4} align="stretch">
          {ordersData.orders.map((order) => (
            <Card.Root
              key={order.id}
              bg="white"
              shadow="sm"
              borderRadius="xl"
              transition="all 0.2s"
              _hover={{ shadow: "md", transform: "translateY(-2px)" }}
            >
              <Card.Body p={6}>
                <VStack gap={6} align="stretch">
                  {/* Order Header */}
                  <Flex justify="space-between" align="start" wrap="wrap" gap={4}>
                    <VStack align="start" gap={2}>
                      <HStack gap={3}>
                        <Icon color="blue.500" fontSize="lg">
                          <FaShoppingBag />
                        </Icon>
                        <Heading size="md" color="gray.800">
                          Order #{order.orderNumber}
                        </Heading>
                      </HStack>
                      <HStack gap={2} color="gray.500" fontSize="sm">
                        <Icon>
                          <FaCalendarAlt />
                        </Icon>
                        <Text>
                          {getFormattedDate(order.createdAt)}
                        </Text>
                      </HStack>
                    </VStack>
                    <VStack gap={2} align="end">
                      <Badge
                        colorScheme={getStatusColor(order.status)}
                        size="lg"
                        borderRadius="full"
                        px={3}
                        py={1}
                      >
                        {order.status.toUpperCase()}
                      </Badge>
                      <Badge
                        colorScheme={getPaymentStatusColor(order.paymentStatus)}
                        size="sm"
                        borderRadius="full"
                        px={2}
                      >
                        {order.paymentStatus.toUpperCase()}
                      </Badge>
                    </VStack>
                  </Flex>

                  {/* Order Items */}
                  <VStack gap={4} align="stretch">
                    {order.items.slice(0, 2).map((item) => (
                      <HStack key={item.id} gap={4} p={3} bg="gray.50" borderRadius="lg">
                        <Image
                          src={item.product.images.find(img => img.isMain)?.imageUrl || '/placeholder.jpg'}
                          alt={item.product.itemName}
                          boxSize="80px"
                          objectFit="cover"
                          borderRadius="lg"
                          shadow="sm"
                        />
                        <VStack align="start" flex={1} gap={2}>
                          <Text fontWeight="semibold" lineClamp={2} color="gray.800">
                            {item.product.itemName}
                          </Text>
                          <HStack gap={4}>
                            <HStack gap={1}>
                              <Text fontSize="sm" color="gray.600">
                                Qty:
                              </Text>
                              <Text fontSize="sm" fontWeight="medium">
                                {item.quantity}
                              </Text>
                            </HStack>
                            <HStack gap={1}>
                              <Text fontSize="sm" color="gray.600">
                                Price:
                              </Text>
                              <Text fontSize="sm" fontWeight="semibold" color="blue.600">
                                {formatPrice(item.price)}
                              </Text>
                            </HStack>
                          </HStack>
                        </VStack>
                      </HStack>
                    ))}
                    {order.items.length > 2 && (
                      <Text fontSize="sm" color="gray.600" textAlign="center" py={2}>
                        +{order.items.length - 2} more items
                      </Text>
                    )}
                  </VStack>

                  {/* Order Summary & Actions */}
                  <Box p={4} bg="gray.50" borderRadius="lg">
                    <Flex justify="space-between" align="center" wrap="wrap" gap={4}>
                      <VStack align="start" gap={1}>
                        <Text fontSize="sm" color="gray.600" fontWeight="medium">
                          Total Amount
                        </Text>
                        <Text fontSize="2xl" fontWeight="bold" color="blue.600">
                          {formatPrice(order.total)}
                        </Text>
                      </VStack>

                      <HStack gap={3}>
                        {/* Pay Now Button for Pending Payments */}
                        {order.paymentStatus === 'pending' && (
                          <Button
                            colorScheme="orange"
                            size="lg"
                            onClick={() => handlePayNow(order)}
                          >
                            <HStack gap={2}>
                              <Icon><FaCreditCard /></Icon>
                              <Text>Pay Now</Text>
                            </HStack>
                          </Button>
                        )}

                        {/* View Order Button */}
                        <Button
                          colorScheme="blue"
                          variant="outline"
                          size="lg"
                          onClick={() => handleViewOrder(order.id)}
                        >
                          <HStack gap={2}>
                            <Icon><FaEye /></Icon>
                            <Text>View Order</Text>
                          </HStack>
                        </Button>
                      </HStack>
                    </Flex>

                    {/* Seller Info */}
                    {(order.items[0]?.product as any)?.seller && (
                      <HStack gap={2} mt={3} pt={3} borderTop="1px" borderColor="gray.200">
                        <Icon color="gray.500" fontSize="sm">
                          <FaUser />
                        </Icon>
                        <Text fontSize="sm" color="gray.600">
                          Seller: {(order.items[0].product as any).seller.firstName} {(order.items[0].product as any).seller.lastName}
                        </Text>
                      </HStack>
                    )}
                  </Box>
                </VStack>
              </Card.Body>
            </Card.Root>
          ))}

          {/* Pagination */}
          {ordersData.pagination.totalPages > 1 && (
            <Card.Root bg="white" shadow="sm" borderRadius="xl">
              <Card.Body p={4}>
                <Flex justify="center" align="center" gap={4}>
                  <Button
                    variant="outline"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage(currentPage - 1)}
                  >
                    <HStack gap={2}>
                      <Icon><FaChevronLeft /></Icon>
                      <Text>Previous</Text>
                    </HStack>
                  </Button>
                  <Text fontWeight="medium" color="gray.600">
                    Page {currentPage} of {ordersData.pagination.totalPages}
                  </Text>
                  <Button
                    variant="outline"
                    disabled={currentPage === ordersData.pagination.totalPages}
                    onClick={() => setCurrentPage(currentPage + 1)}
                  >
                    <HStack gap={2}>
                      <Text>Next</Text>
                      <Icon><FaChevronRight /></Icon>
                    </HStack>
                  </Button>
                </Flex>
              </Card.Body>
            </Card.Root>
          )}
        </VStack>
      )}
    </VStack>
  )
}

export default AccountBuyingPage