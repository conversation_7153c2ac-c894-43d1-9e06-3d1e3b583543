'use client'
import React, { useState } from 'react'
import {
    Box,
    <PERSON>ton,
    Card,
    Heading,
    Text,
    VStack,
    HStack,
    Image,
    Badge,
    Grid,
    Menu,
    Skeleton,
    Flex,
    Portal,
    Icon,
    InputGroup,
    Input,
} from '@chakra-ui/react'
import {
    FaEye,
    FaEllipsisV,
    FaGavel,
    FaShoppingCart,
    FaClock,
    FaDollarSign,
    FaPlusCircle,
    FaSearch
} from 'react-icons/fa'
import { useProductsQuery, useDeleteProductMutation, ProductQueryParams } from '@/services/useProductQuery'
import { formatUSD } from '@/utils/helpers/helper'
import { formatDistanceToNow } from 'date-fns'
import { useRouter } from 'next/navigation'
import FormSelectField, { SelectOption } from '@/components/ui/form/FormSelectField'
import { SingleValue } from 'react-select'
import { Alert, AlertDescription, AlertIcon, AlertTitle } from '@/components/ui/alert'
import _ from 'lodash'
import { useSession } from 'next-auth/react'
import { useTranslations } from 'next-intl'
import Link from 'next/link'

const AccountSellingPage = () => {
    const router = useRouter()
    const t = useTranslations();
    const dataSession = useSession()
    const session = dataSession.data;
    const [searchTerm, setSearchTerm] = useState('')
    const [statusFilter, setStatusFilter] = useState<string>('all')
    const [sellTypeFilter, setSellTypeFilter] = useState<string>('all')

    // Query parameters for user's products
    const queryParams: ProductQueryParams = {
        sellerId: session?.user?.id || '',
        search: searchTerm || undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        sellType: sellTypeFilter !== 'all' ? (sellTypeFilter as 'auction' | 'buy-now') : undefined,
        sortBy: 'createdAt',
        sortOrder: 'desc',
        limit: 20
    }

    const { data: productsData, isLoading, error } = useProductsQuery(queryParams)
    const deleteProductMutation = useDeleteProductMutation()

    const handleSearch = (value: string) => _.debounce((searchValue: string) => {
        setSearchTerm(searchValue)
    }, 500)

    const handleDeleteProduct = async (productId: string, productName: string) => {
        if (window.confirm(`Are you sure you want to delete "${productName}"? This action cannot be undone.`)) {
            try {
                await deleteProductMutation.mutateAsync(productId)
            } catch (error) {
                console.error('Failed to delete product:', error)
            }
        }
    }

    const handleEditProduct = (productId: string) => {
        router.push(`/selling/edit/${productId}`)
    }

    const handleViewProduct = (slug: string) => {
        router.push(`/products/${slug}`)
    }

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active': return 'green'
            case 'draft': return 'gray'
            case 'sold': return 'blue'
            case 'cancelled': return 'red'
            default: return 'gray'
        }
    }

    const getStatusText = (status: string) => {
        switch (status) {
            case 'active': return 'Active'
            case 'draft': return 'Draft'
            case 'sold': return 'Sold'
            case 'cancelled': return 'Cancelled'
            default: return status
        }
    }

    if (dataSession.status !== "loading" && !session) {
        return (
            <Box maxW="6xl" mx="auto" p={6} textAlign="center">
                <Alert status="warning">
                    <AlertIcon />
                    <AlertTitle>Please log in!</AlertTitle>
                    <AlertDescription>
                        You need to be logged in to view your selling items.
                    </AlertDescription>
                </Alert>
                <Button mt={4} onClick={() => router.push('/auth/login')}>
                    Log In
                </Button>
            </Box>
        )
    }

    const products = productsData?.products || []
    const activeProducts = products.filter(p => p.status === 'active')
    const soldProducts = products.filter(p => p.status === 'sold')
    const totalEarnings = soldProducts.reduce((sum, p) => sum + Number(p.priceUSD), 0)

    return (
        <Box maxW="6xl" mx="auto" p={6}>
            {/* Header */}
            <Flex justify="space-between" align="center" mb={6}>
                <VStack align="start" gap={1}>
                    <Heading size="xl">My Selling Items</Heading>
                    <Text color="gray.600">
                        Manage your collectibles and auctions
                    </Text>
                </VStack>
                <Button
                    colorScheme="blue"
                    onClick={() => router.push('/selling/create')}
                >
                    <FaPlusCircle style={{ marginRight: '8px' }} />
                    {t('Button.sellNow')}
                </Button>
            </Flex>

            {/* Stats Cards */}
            <Grid templateColumns={{ base: "1fr", md: "repeat(4, 1fr)" }} gap={4} mb={6}>
                <Card.Root>
                    <Card.Body textAlign="center">
                        <VStack gap={1}>
                            <Text fontSize="xs" color="gray.500" textTransform="uppercase">Total Items</Text>
                            <Text fontSize="2xl" fontWeight="bold" color="gray.800">{products.length}</Text>
                            <Text fontSize="xs" color="gray.500">All time</Text>
                        </VStack>
                    </Card.Body>
                </Card.Root>

                <Card.Root>
                    <Card.Body textAlign="center">
                        <VStack gap={1}>
                            <Text fontSize="xs" color="gray.500" textTransform="uppercase">Active Listings</Text>
                            <Text fontSize="2xl" fontWeight="bold" color="green.600">{activeProducts.length}</Text>
                            <Text fontSize="xs" color="gray.500">Currently selling</Text>
                        </VStack>
                    </Card.Body>
                </Card.Root>

                <Card.Root>
                    <Card.Body textAlign="center">
                        <VStack gap={1}>
                            <Text fontSize="xs" color="gray.500" textTransform="uppercase">Items Sold</Text>
                            <Text fontSize="2xl" fontWeight="bold" color="purple.600">{soldProducts.length}</Text>
                            <Text fontSize="xs" color="gray.500">Completed sales</Text>
                        </VStack>
                    </Card.Body>
                </Card.Root>

                <Card.Root>
                    <Card.Body textAlign="center">
                        <VStack gap={1}>
                            <Text fontSize="xs" color="gray.500" textTransform="uppercase">Total Earnings</Text>
                            <Text fontSize="2xl" fontWeight="bold" color="orange.600">{formatUSD(totalEarnings)}</Text>
                            <Text fontSize="xs" color="gray.500">From sold items</Text>
                        </VStack>
                    </Card.Body>
                </Card.Root>
            </Grid>

            {/* Filters */}
            <Card.Root mb={6}>
                <Card.Body>
                    <Grid templateColumns={{ base: "1fr", md: "2fr 1fr 1fr" }} gap={4}>

                        <InputGroup startElement={<FaSearch />}>
                            <Input
                                placeholder="Search your items..."
                                onChange={(e) => handleSearch(e.target.value)}
                            />
                        </InputGroup>

                        <FormSelectField
                            placeholder='Select Status'
                            options={[
                                { value: 'all', label: 'All Status' },
                                { value: 'active', label: 'Active' },
                                { value: 'draft', label: 'Draft' },
                                { value: 'sold', label: 'Sold' },
                                { value: 'cancelled', label: 'Cancelled' }
                            ]}
                            onChange={(selectedOption) => {
                                const option = selectedOption as SingleValue<SelectOption>;
                                setStatusFilter(option?.value || 'all')
                            }}
                        />

                        <FormSelectField
                            placeholder='Select Sell Type'
                            options={[
                                { value: 'all', label: 'All Types' },
                                { value: 'auction', label: 'Auction' },
                                { value: 'buy-now', label: 'Buy Now' }
                            ]}
                            onChange={(selectedOption) => {
                                const option = selectedOption as SingleValue<SelectOption>;
                                setSellTypeFilter(option?.value || 'all')
                            }}
                        />
                    </Grid>
                </Card.Body>
            </Card.Root>

            {/* Products List */}
            {isLoading ? (
                <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }} gap={6}>
                    {[...Array(6)].map((_, i) => (
                        <Card.Root key={i}>
                            <Card.Body>
                                <VStack gap={4}>
                                    <Skeleton height="200px" width="100%" />
                                    <Skeleton height="20px" width="80%" />
                                    <Skeleton height="16px" width="60%" />
                                </VStack>
                            </Card.Body>
                        </Card.Root>
                    ))}
                </Grid>
            ) : error ? (
                <Alert status="error">
                    <AlertIcon />
                    <AlertTitle>Error loading products!</AlertTitle>
                    <AlertDescription>
                        There was an error loading your selling items. Please try again.
                    </AlertDescription>
                </Alert>
            ) : products.length === 0 ? (
                <Card.Root>
                    <Card.Body textAlign="center" py={12}>
                        <VStack gap={4}>
                            <Box fontSize="4xl">📦</Box>
                            <Heading size="md" color="gray.600">No items found</Heading>
                            <Text color="gray.500">
                                {searchTerm || statusFilter !== 'all' || sellTypeFilter !== 'all'
                                    ? 'No items match your current filters.'
                                    : "You haven't listed any items yet."
                                }
                            </Text>
                            <Button
                                colorScheme="blue"
                                onClick={() => router.push('/selling/create')}
                            >
                                <FaPlusCircle style={{ marginRight: '8px' }} />
                                {t('Button.sellNow')}
                            </Button>
                        </VStack>
                    </Card.Body>
                </Card.Root>
            ) : (
                <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }} gap={6}>
                    {products.map((product) => {
                        const mainImage = product.images.find(img => img.isMain) || product.images[0]
                        const isAuction = product.sellType === 'auction'
                        const timeLeft = product.auctionEndDate
                            ? formatDistanceToNow(new Date(product.auctionEndDate), { addSuffix: true })
                            : null

                        return (
                            <Card.Root key={product.id} overflow="hidden">
                                <Box position="relative">
                                    <Image
                                        src={mainImage?.imageUrl}
                                        alt={product.itemName}
                                        height="200px"
                                        width="100%"
                                        objectFit="cover"
                                    />
                                    <Badge
                                        position="absolute"
                                        top={2}
                                        left={2}
                                        colorScheme={getStatusColor(product.status)}
                                        fontWeight="bold"
                                    >
                                        {getStatusText(product.status)}
                                    </Badge>
                                    <Badge
                                        position="absolute"
                                        top={2}
                                        right={2}
                                        colorScheme={isAuction ? 'blue' : 'green'}
                                    >
                                        {isAuction ? <FaGavel /> : <FaShoppingCart />}
                                        {isAuction ? 'Auction' : 'Buy Now'}
                                    </Badge>
                                </Box>

                                <Card.Body p={4}>
                                    <VStack align="stretch" gap={3}>
                                        <HStack alignItems="start" justify="space-between">
                                            <Link href={`/products/${product.slug || product.id}`} style={{ textDecoration: 'none' }}>
                                                <Text fontWeight="bold" fontSize="md" lineClamp={2} mb={1}>
                                                    {product.itemName}
                                                </Text>
                                                <Text fontSize="sm" color="gray.600">
                                                    {product.category?.name}
                                                </Text>
                                            </Link>

                                            <Menu.Root>
                                                <Menu.Trigger asChild cursor={'pointer'}>
                                                    <Icon as={FaEllipsisV} boxSize={4} />
                                                </Menu.Trigger>
                                                <Portal>
                                                    <Menu.Positioner>
                                                        <Menu.Content>
                                                            <Menu.Item value="view"
                                                                onClick={() => handleViewProduct(product.slug || product.id)}
                                                            >
                                                                <FaEye style={{ marginRight: '8px' }} />
                                                                View
                                                            </Menu.Item>
                                                            <Menu.Item value="extend-auction"

                                                            >
                                                                <FaDollarSign style={{ marginRight: '8px' }} />
                                                                Extend Auction
                                                            </Menu.Item>
                                                            {/* <Menu.Item value="new-txt"
                                                                onClick={() => handleEditProduct(product.id)}
                                                            >
                                                                <FaEdit style={{ marginRight: '8px' }} />
                                                                Edit
                                                            </Menu.Item> */}
                                                            {/* <Menu.Item
                                                                value="delete"
                                                                color="red.600"
                                                                onClick={() => handleDeleteProduct(product.id, product.itemName)}
                                                            >
                                                                <FaTrash style={{ marginRight: '8px' }} />
                                                                Delete
                                                            </Menu.Item> */}
                                                        </Menu.Content>
                                                    </Menu.Positioner>
                                                </Portal>
                                            </Menu.Root>
                                        </HStack>
                                        <HStack justify="space-between">
                                            <VStack align="start" gap={0}>
                                                <Text fontSize="xs" color="gray.500">
                                                    {isAuction ? 'Current Bid' : 'Price'}
                                                </Text>
                                                <Text fontWeight="bold" fontSize="xl" color="gray.800">
                                                    {formatUSD(Number(product.currentBid || product.priceUSD))}
                                                </Text>
                                            </VStack>
                                            {isAuction && (
                                                <VStack align="end" gap={0}>
                                                    <Text fontSize="xs" color="gray.500">
                                                        Bids
                                                    </Text>
                                                    <Text fontWeight="bold" fontSize="xl">
                                                        {product.bidCount || 0}
                                                    </Text>
                                                </VStack>
                                            )}
                                        </HStack>

                                        {isAuction && timeLeft && product.status === 'active' && (
                                            <HStack>
                                                <FaClock size={12} color="gray" />
                                                <Text fontSize="xs" color="gray.600">
                                                    {timeLeft}
                                                </Text>
                                            </HStack>
                                        )}
                                    </VStack>
                                </Card.Body>
                            </Card.Root>
                        )
                    })}
                </Grid>
            )}
        </Box>
    )
}

export default AccountSellingPage
