# ========== BUILD DEPS ==========
FROM node:20-alpine AS deps
WORKDIR /app

# Install dependencies
COPY package.json package-lock.json ./
RUN npm ci --only=production && npm cache clean --force

# ========== BUILD APP ==========
FROM node:20-alpine AS builder
WORKDIR /app

# Install all dependencies (including dev dependencies for build)
COPY package.json package-lock.json ./
RUN npm ci

# Copy source code
COPY . .

# Set dummy DATABASE_URL for build (Prisma client generation)
ENV DATABASE_URL="mysql://dummy:dummy@localhost:3306/dummy"

# Generate Prisma client (no database connection needed)
RUN npx prisma generate

# Build the application (without database operations)
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production
RUN npm run build:docker

# Verify build output
RUN ls -la .next/ && ls -la public/ && echo "Build verification complete"

# ========== RUNNER ==========
FROM node:20-alpine AS runner
WORKDIR /app

# Install runtime dependencies
RUN apk add --no-cache \
    dumb-init \
    curl \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Copy built application
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# Copy public directory (Next.js standalone doesn't include public by default)
COPY --from=builder /app/public ./public

# Copy Prisma files for runtime migrations
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/node_modules/@prisma ./node_modules/@prisma

# Copy scripts for runtime operations
# COPY --from=builder /app/scripts ./scripts

# Copy package.json and node_modules for runtime
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/node_modules ./node_modules

# Create startup script
COPY <<EOF /app/start.sh
#!/bin/sh
set -e

echo "🔍 Waiting for database to be ready..."
# Wait for database with timeout
TIMEOUT=300  # 5 minutes
COUNTER=0
until npx prisma db push --accept-data-loss 2>/dev/null; do
  if [ \$COUNTER -ge \$TIMEOUT ]; then
    echo "❌ Database connection timeout after 5 minutes"
    exit 1
  fi
  echo "⏳ Database not ready, retrying in 5 seconds... (\$COUNTER/\$TIMEOUT)"
  sleep 5
  COUNTER=\$((COUNTER + 5))
done

echo "✅ Database connection established"

echo "🔄 Running database migrations..."
if npx prisma migrate deploy; then
  echo "✅ Migrations completed successfully"
else
  echo "⚠️ Migration failed, trying to push schema..."
  if npx prisma db push --accept-data-loss; then
    echo "✅ Schema push completed"
  else
    echo "❌ Schema operations failed"
    exit 1
  fi
fi

echo "🌱 Running database seeding..."
if npm run db:seed:production; then
  echo "✅ Database seeding completed"
else
  echo "⚠️ Seeding failed, but continuing with application startup..."
fi

echo "🚀 Starting application..."
exec node server.js
EOF

RUN chmod +x /app/start.sh && \
    chown -R nextjs:nodejs /app

USER nextjs
EXPOSE 3000

CMD ["npm", "start"]

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1
